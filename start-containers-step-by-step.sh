#!/bin/bash

# Step-by-step Container Startup for Tuchanga
# This script starts containers one by one to avoid overwhelming the EC2 instance

set -e

echo "🚀 Tuchanga Step-by-Step Container Startup"
echo "=========================================="
echo ""

# Function to wait for a service to be ready
wait_for_service() {
    local service_name=$1
    local check_command=$2
    local max_attempts=30
    local attempt=1
    
    echo "⏳ Waiting for $service_name to be ready..."
    
    while [ $attempt -le $max_attempts ]; do
        if eval "$check_command" >/dev/null 2>&1; then
            echo "✅ $service_name is ready!"
            return 0
        fi
        
        echo "   Attempt $attempt/$max_attempts - waiting 10 seconds..."
        sleep 10
        ((attempt++))
    done
    
    echo "❌ $service_name failed to start after $max_attempts attempts"
    return 1
}

# Function to check container status
check_container_status() {
    local container_name=$1
    if docker ps --filter "name=$container_name" --format "{{.Names}}" | grep -q "$container_name"; then
        local status=$(docker ps --filter "name=$container_name" --format "{{.Status}}")
        echo "✅ $container_name: $status"
        return 0
    else
        echo "❌ $container_name: Not running"
        return 1
    fi
}

# Function to show resource usage
show_resources() {
    echo "📊 Current resource usage:"
    echo "Memory:"
    free -h | head -2
    echo "CPU Load:"
    uptime
    echo ""
}

echo "🔍 Step 1: Pre-flight checks..."

# Check Docker
if ! docker info >/dev/null 2>&1; then
    echo "❌ Docker is not running. Starting Docker..."
    sudo systemctl start docker
    sleep 5
fi

# Stop any existing containers
echo "🛑 Stopping existing containers..."
docker-compose down --remove-orphans || true

# Show initial resources
show_resources

echo "🗄️ Step 2: Starting PostgreSQL Database..."
echo "============================================"
docker-compose up -d postgres

echo "Checking PostgreSQL container status..."
sleep 5
check_container_status "job-platform-postgres"

echo "Waiting for PostgreSQL to accept connections..."
wait_for_service "PostgreSQL" "docker exec job-platform-postgres pg_isready -U postgres -d job_platform"

echo "✅ PostgreSQL is ready!"
show_resources

echo ""
echo "🔧 Step 3: Starting Backend API..."
echo "=================================="
docker-compose up -d backend

echo "Checking Backend container status..."
sleep 10
check_container_status "job-platform-backend"

echo "Waiting for Backend API to be healthy..."
wait_for_service "Backend API" "docker exec job-platform-backend curl -f http://localhost:3000/health"

echo "✅ Backend API is ready!"
show_resources

echo ""
echo "🌐 Step 4: Starting Frontend..."
echo "==============================="
docker-compose up -d frontend

echo "Checking Frontend container status..."
sleep 10
check_container_status "job-platform-frontend"

echo "Waiting for Frontend to serve content..."
wait_for_service "Frontend" "docker exec job-platform-frontend curl -f http://localhost:80"

echo "✅ Frontend is ready!"
show_resources

echo ""
echo "🔍 Step 5: Final verification..."
echo "================================"

echo "Container status summary:"
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" --filter "name=job-platform"

echo ""
echo "Health checks:"

# Test database
if docker exec job-platform-postgres pg_isready -U postgres -d job_platform >/dev/null 2>&1; then
    echo "✅ Database: Healthy"
else
    echo "❌ Database: Unhealthy"
fi

# Test backend
backend_health=$(docker exec job-platform-backend curl -s http://localhost:3000/health 2>/dev/null || echo "failed")
if echo "$backend_health" | grep -q "healthy"; then
    echo "✅ Backend API: Healthy"
else
    echo "❌ Backend API: Unhealthy"
    echo "   Response: $backend_health"
fi

# Test frontend
frontend_response=$(docker exec job-platform-frontend curl -s -o /dev/null -w "%{http_code}" http://localhost:80 2>/dev/null || echo "failed")
if [[ "$frontend_response" == "200" ]]; then
    echo "✅ Frontend: Healthy"
else
    echo "❌ Frontend: Unhealthy (HTTP $frontend_response)"
fi

echo ""
echo "🌐 Testing external connectivity..."

# Get public IP
public_ip=$(curl -s http://checkip.amazonaws.com/ || echo 'unknown')
echo "Public IP: $public_ip"

# Test ports
echo "Port accessibility:"
for port in 80 443 3000 5432; do
    if netstat -tuln | grep -q ":$port "; then
        echo "✅ Port $port: Listening"
    else
        echo "❌ Port $port: Not listening"
    fi
done

echo ""
echo "🎉 Startup Complete!"
echo "===================="

if [[ "$public_ip" != "unknown" ]]; then
    echo "🌐 Your application should be accessible at:"
    echo "   HTTP:  http://$public_ip"
    echo "   API:   http://$public_ip:3000/health"
    echo "   Proxy: http://$public_ip/api/health"
    echo ""
fi

echo "📋 Next steps:"
echo "1. Test the application in your browser"
echo "2. If using a domain, make sure DNS points to $public_ip"
echo "3. For HTTPS, run: ./setup-https.sh"
echo ""

echo "🔍 For troubleshooting, run:"
echo "   ./diagnose-deployment.sh"
echo "   ./check-api-config.sh"
echo ""

final_resources() {
    echo "📊 Final resource usage:"
    free -h | head -2
    uptime
    echo ""
    echo "Docker stats (5 second snapshot):"
    timeout 5 docker stats --no-stream --format "table {{.Name}}\t{{.CPUPerc}}\t{{.MemUsage}}" || echo "Could not get docker stats"
}

final_resources

echo "✅ All done! Your Tuchanga platform should be running."
