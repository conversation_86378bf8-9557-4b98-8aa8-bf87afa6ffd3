#!/bin/bash

# Quick Fix Script for Tuchanga Deployment Issues
# This script attempts to fix common deployment problems

set -e

echo "🔧 Tuchanga Deployment Quick Fix"
echo "================================"
echo ""

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to wait for a service to be ready
wait_for_service() {
    local service_name=$1
    local check_command=$2
    local max_attempts=30
    local attempt=1
    
    echo "⏳ Waiting for $service_name to be ready..."
    
    while [ $attempt -le $max_attempts ]; do
        if eval "$check_command" >/dev/null 2>&1; then
            echo "✅ $service_name is ready!"
            return 0
        fi
        
        echo "   Attempt $attempt/$max_attempts - waiting..."
        sleep 10
        ((attempt++))
    done
    
    echo "❌ $service_name failed to start after $max_attempts attempts"
    return 1
}

echo "🔍 Step 1: Checking prerequisites..."

# Check if Docker is installed and running
if ! command_exists docker; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

if ! docker info >/dev/null 2>&1; then
    echo "🔄 Starting Docker daemon..."
    sudo systemctl start docker
    sudo systemctl enable docker
    sleep 5
fi

# Check if Docker Compose is available
if ! command_exists docker-compose; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

echo "✅ Prerequisites check passed"
echo ""

echo "🛑 Step 2: Stopping existing containers..."
docker-compose down --remove-orphans || true
echo ""

echo "🧹 Step 3: Cleaning up Docker resources..."
# Remove any dangling containers
docker container prune -f || true
# Remove unused networks
docker network prune -f || true
echo ""

echo "🔐 Step 4: Checking SSL certificates..."
if [[ ! -f "./ssl/cert.pem" ]] || [[ ! -f "./ssl/key.pem" ]]; then
    echo "⚠️  SSL certificates not found. Creating self-signed certificates..."
    mkdir -p ./ssl
    
    # Generate self-signed certificate
    openssl genrsa -out ./ssl/key.pem 2048
    openssl req -new -key ./ssl/key.pem -out ./ssl/cert.csr -subj "/C=US/ST=State/L=City/O=Tuchanga/CN=tuchanga.com"
    openssl x509 -req -days 365 -in ./ssl/cert.csr -signkey ./ssl/key.pem -out ./ssl/cert.pem
    rm ./ssl/cert.csr
    
    # Set proper permissions
    chmod 644 ./ssl/cert.pem
    chmod 600 ./ssl/key.pem
    
    echo "✅ Self-signed SSL certificates created"
else
    echo "✅ SSL certificates found"
fi
echo ""

echo "🔥 Step 5: Configuring firewall..."
# Configure UFW if available
if command_exists ufw; then
    echo "Configuring UFW firewall..."
    sudo ufw --force enable
    sudo ufw allow 22/tcp    # SSH
    sudo ufw allow 80/tcp    # HTTP
    sudo ufw allow 443/tcp   # HTTPS
    sudo ufw allow 3000/tcp  # Backend API
    echo "✅ UFW firewall configured"
elif command_exists firewall-cmd; then
    echo "Configuring firewalld..."
    sudo systemctl start firewalld
    sudo systemctl enable firewalld
    sudo firewall-cmd --permanent --add-service=http
    sudo firewall-cmd --permanent --add-service=https
    sudo firewall-cmd --permanent --add-port=3000/tcp
    sudo firewall-cmd --reload
    echo "✅ Firewalld configured"
else
    echo "⚠️  No firewall detected. Make sure your AWS Security Group allows ports 80, 443, and 3000"
fi
echo ""

echo "🐳 Step 6: Building and starting containers one by one..."

# Pull latest images
echo "Pulling latest images..."
docker-compose pull || true

# Build containers
echo "Building containers..."
docker-compose build --no-cache

echo ""
echo "🗄️ Step 6.1: Starting PostgreSQL database..."
docker-compose up -d postgres
echo "⏳ Waiting for PostgreSQL to be ready..."
wait_for_service "PostgreSQL" "docker exec job-platform-postgres pg_isready -U postgres"

echo ""
echo "🔧 Step 6.2: Starting Backend API..."
docker-compose up -d backend
echo "⏳ Waiting for Backend to be ready..."
wait_for_service "Backend API" "docker exec job-platform-backend curl -f http://localhost:3000/health"

echo ""
echo "🌐 Step 6.3: Starting Frontend..."
docker-compose up -d frontend
echo "⏳ Waiting for Frontend to be ready..."
wait_for_service "Frontend" "docker exec job-platform-frontend curl -f http://localhost:80"

echo ""
echo "✅ All containers started successfully"
echo ""

echo "⏳ Step 7: Final service verification..."

echo ""

echo "🌐 Step 8: Testing connectivity..."

# Get public IP
public_ip=$(curl -s http://checkip.amazonaws.com/ || echo 'unknown')
echo "Your public IP: $public_ip"

# Test HTTP
echo "Testing HTTP connectivity..."
if curl -s -o /dev/null -w "%{http_code}" http://localhost:80 | grep -q "200\|301\|302"; then
    echo "✅ HTTP (port 80) is working"
else
    echo "❌ HTTP (port 80) is not responding"
fi

# Test HTTPS
echo "Testing HTTPS connectivity..."
if curl -k -s -o /dev/null -w "%{http_code}" https://localhost:443 | grep -q "200\|301\|302"; then
    echo "✅ HTTPS (port 443) is working"
else
    echo "❌ HTTPS (port 443) is not responding"
fi

# Test Backend API
echo "Testing Backend API..."
if curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/health | grep -q "200"; then
    echo "✅ Backend API (port 3000) is working"
else
    echo "❌ Backend API (port 3000) is not responding"
fi

echo ""

echo "📊 Step 9: Final status check..."
echo "Container status:"
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
echo ""

echo "🎉 Deployment fix completed!"
echo ""
echo "🌐 Your application should now be accessible at:"
echo "   HTTP:  http://$public_ip"
echo "   HTTPS: https://$public_ip (self-signed certificate)"
echo ""
echo "🔗 If you have a domain configured:"
echo "   HTTP:  http://tuchanga.com"
echo "   HTTPS: https://tuchanga.com"
echo "   API:   https://api.tuchanga.com"
echo ""
echo "⚠️  Note: If using self-signed certificates, browsers will show security warnings."
echo "   For production, use Let's Encrypt certificates with: ./setup-https.sh"
echo ""
echo "📋 Next steps:"
echo "1. Test your application in a browser"
echo "2. Check AWS Security Group settings if still not accessible"
echo "3. Update DNS A records if using a custom domain"
echo "4. Set up proper SSL certificates for production"
echo ""
echo "🔍 For detailed diagnosis, run: ./diagnose-deployment.sh"
