#!/bin/bash

# API Configuration Checker for Tuchanga
# This script verifies API connectivity and configuration

set -e

echo "🔍 Tuchanga API Configuration Check"
echo "==================================="
echo ""

# Get current public IP
public_ip=$(curl -s http://checkip.amazonaws.com/ || echo 'unknown')
echo "📍 Current public IP: $public_ip"
echo ""

echo "🔗 Testing API Endpoints:"
echo "-------------------------"

# Test backend health directly
echo "1. Testing backend container health (internal):"
if docker ps --filter "name=job-platform-backend" --format "{{.Names}}" | grep -q "job-platform-backend"; then
    backend_response=$(docker exec job-platform-backend curl -s http://localhost:3000/health 2>/dev/null || echo "failed")
    if echo "$backend_response" | grep -q "healthy"; then
        echo "   ✅ Backend container health: OK"
    else
        echo "   ❌ Backend container health: Failed"
        echo "   Response: $backend_response"
    fi
else
    echo "   ❌ Backend container not running"
fi

# Test backend via host port
echo ""
echo "2. Testing backend via host port 3000:"
backend_host_response=$(curl -s http://localhost:3000/health 2>/dev/null || echo "failed")
if echo "$backend_host_response" | grep -q "healthy"; then
    echo "   ✅ Backend host port: OK"
else
    echo "   ❌ Backend host port: Failed"
    echo "   Response: $backend_host_response"
fi

# Test backend via public IP
echo ""
echo "3. Testing backend via public IP:"
if [[ "$public_ip" != "unknown" ]]; then
    backend_public_response=$(curl -s http://$public_ip:3000/health 2>/dev/null || echo "failed")
    if echo "$backend_public_response" | grep -q "healthy"; then
        echo "   ✅ Backend public IP: OK"
    else
        echo "   ❌ Backend public IP: Failed"
        echo "   Response: $backend_public_response"
    fi
else
    echo "   ⚠️  Cannot test - public IP unknown"
fi

# Test frontend nginx proxy
echo ""
echo "4. Testing frontend nginx proxy (/api/):"
frontend_proxy_response=$(curl -s http://localhost/api/health 2>/dev/null || echo "failed")
if echo "$frontend_proxy_response" | grep -q "healthy"; then
    echo "   ✅ Frontend proxy: OK"
else
    echo "   ❌ Frontend proxy: Failed"
    echo "   Response: $frontend_proxy_response"
fi

# Test HTTPS frontend proxy
echo ""
echo "5. Testing HTTPS frontend proxy (/api/):"
frontend_https_response=$(curl -k -s https://localhost/api/health 2>/dev/null || echo "failed")
if echo "$frontend_https_response" | grep -q "healthy"; then
    echo "   ✅ HTTPS frontend proxy: OK"
else
    echo "   ❌ HTTPS frontend proxy: Failed"
    echo "   Response: $frontend_https_response"
fi

echo ""
echo "🌐 Frontend Configuration:"
echo "--------------------------"

# Check if frontend container is running
if docker ps --filter "name=job-platform-frontend" --format "{{.Names}}" | grep -q "job-platform-frontend"; then
    echo "✅ Frontend container is running"

    # Check nginx configuration
    echo ""
    echo "📋 Nginx configuration test:"
    nginx_test=$(docker exec job-platform-frontend nginx -t 2>&1)
    if echo "$nginx_test" | grep -q "successful"; then
        echo "✅ Nginx configuration is valid"
    else
        echo "❌ Nginx configuration has errors:"
        echo "$nginx_test"
    fi

    # Check if API proxy is configured
    echo ""
    echo "📋 API proxy configuration:"
    if docker exec job-platform-frontend cat /etc/nginx/conf.d/default.conf | grep -q "location /api/"; then
        echo "✅ API proxy configuration found"

        # Show proxy configuration
        echo "Proxy configuration:"
        docker exec job-platform-frontend grep -A 10 "location /api/" /etc/nginx/conf.d/default.conf | head -8
    else
        echo "❌ API proxy configuration missing"
    fi

    # Check SSL certificate mounting
    echo ""
    echo "📋 SSL certificate check:"
    if docker exec job-platform-frontend ls /etc/nginx/ssl/ 2>/dev/null | grep -q "cert.pem"; then
        echo "✅ SSL certificates mounted"
    else
        echo "⚠️  SSL certificates not mounted (HTTPS won't work)"
    fi

    # Check if frontend files are present
    echo ""
    echo "📋 Frontend files check:"
    if docker exec job-platform-frontend ls /usr/share/nginx/html/index.html >/dev/null 2>&1; then
        echo "✅ Frontend files present"

        # Check if it's a React app
        if docker exec job-platform-frontend grep -q "React" /usr/share/nginx/html/index.html 2>/dev/null; then
            echo "✅ React application detected"
        else
            echo "⚠️  React application not detected in index.html"
        fi
    else
        echo "❌ Frontend files missing"
    fi

    # Test nginx processes
    echo ""
    echo "📋 Nginx process check:"
    nginx_processes=$(docker exec job-platform-frontend ps aux | grep nginx | grep -v grep | wc -l)
    if [ "$nginx_processes" -gt 0 ]; then
        echo "✅ Nginx processes running ($nginx_processes processes)"
    else
        echo "❌ No nginx processes found"
    fi

else
    echo "❌ Frontend container not running"
fi

echo ""
echo "🔧 Backend CORS Configuration:"
echo "------------------------------"

# Check backend logs for CORS issues
echo "Recent backend logs (looking for CORS issues):"
if docker ps --filter "name=job-platform-backend" --format "{{.Names}}" | grep -q "job-platform-backend"; then
    docker logs --tail 20 job-platform-backend 2>&1 | grep -i -E "(cors|origin|error)" || echo "No CORS-related logs found"
else
    echo "❌ Backend container not running"
fi

echo ""
echo "🌍 Domain and DNS Check:"
echo "------------------------"

# Test domain resolution
domain="tuchanga.com"
if nslookup $domain &> /dev/null; then
    resolved_ip=$(nslookup $domain | grep -A1 "Name:" | tail -1 | awk '{print $2}')
    echo "Domain $domain resolves to: $resolved_ip"
    echo "Your public IP is: $public_ip"
    
    if [[ "$resolved_ip" == "$public_ip" ]]; then
        echo "✅ DNS correctly points to this server"
        
        # Test domain endpoints
        echo ""
        echo "Testing domain endpoints:"
        
        # Test HTTP
        domain_http_response=$(curl -s http://$domain/api/health 2>/dev/null || echo "failed")
        if echo "$domain_http_response" | grep -q "healthy"; then
            echo "   ✅ http://$domain/api/health: OK"
        else
            echo "   ❌ http://$domain/api/health: Failed"
        fi
        
        # Test HTTPS
        domain_https_response=$(curl -k -s https://$domain/api/health 2>/dev/null || echo "failed")
        if echo "$domain_https_response" | grep -q "healthy"; then
            echo "   ✅ https://$domain/api/health: OK"
        else
            echo "   ❌ https://$domain/api/health: Failed"
        fi
        
        # Test API subdomain
        api_subdomain_response=$(curl -k -s https://api.$domain/health 2>/dev/null || echo "failed")
        if echo "$api_subdomain_response" | grep -q "healthy"; then
            echo "   ✅ https://api.$domain/health: OK"
        else
            echo "   ❌ https://api.$domain/health: Failed"
        fi
        
    else
        echo "❌ DNS not pointing to this server"
    fi
else
    echo "❌ Domain $domain does not resolve"
fi

echo ""
echo "📊 Port Status:"
echo "---------------"
echo "Checking if required ports are listening:"
for port in 80 443 3000 5432; do
    if netstat -tuln | grep -q ":$port "; then
        echo "✅ Port $port: Listening"
    else
        echo "❌ Port $port: Not listening"
    fi
done

echo ""
echo "🔍 Container Network Status:"
echo "----------------------------"
echo "Docker network information:"
docker network ls | grep job-platform || echo "No job-platform network found"

echo ""
echo "Container connectivity test:"
if docker ps --filter "name=job-platform-frontend" --format "{{.Names}}" | grep -q "job-platform-frontend"; then
    if docker ps --filter "name=job-platform-backend" --format "{{.Names}}" | grep -q "job-platform-backend"; then
        # Test if frontend can reach backend
        frontend_to_backend=$(docker exec job-platform-frontend curl -s http://backend:3000/health 2>/dev/null || echo "failed")
        if echo "$frontend_to_backend" | grep -q "healthy"; then
            echo "✅ Frontend can reach backend via Docker network"
        else
            echo "❌ Frontend cannot reach backend via Docker network"
            echo "   Response: $frontend_to_backend"
        fi
    else
        echo "❌ Backend container not running"
    fi
else
    echo "❌ Frontend container not running"
fi

echo ""
echo "🎯 Recommendations:"
echo "-------------------"

# Provide specific recommendations based on test results
if ! docker ps --filter "name=job-platform-backend" --format "{{.Names}}" | grep -q "job-platform-backend"; then
    echo "1. ❌ Start the backend container: docker-compose up -d backend"
fi

if ! docker ps --filter "name=job-platform-frontend" --format "{{.Names}}" | grep -q "job-platform-frontend"; then
    echo "2. ❌ Start the frontend container: docker-compose up -d frontend"
fi

if ! netstat -tuln | grep -q ":80 "; then
    echo "3. ❌ Port 80 not accessible - check firewall and AWS Security Group"
fi

if ! netstat -tuln | grep -q ":443 "; then
    echo "4. ❌ Port 443 not accessible - check SSL setup and firewall"
fi

if [[ "$public_ip" != "unknown" ]]; then
    echo ""
    echo "🌐 Test URLs:"
    echo "   HTTP:  http://$public_ip"
    echo "   HTTPS: https://$public_ip (may show certificate warning)"
    echo "   API:   http://$public_ip:3000/health"
    echo "   API via proxy: http://$public_ip/api/health"
fi

echo ""
echo "✅ API configuration check complete!"
